package com.misyn.mcms.log;

import com.misyn.mcms.dbconfig.ConnectionPool;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
public class UserLog {
    private static final Logger LOGGER = LoggerFactory.getLogger(UserLog.class);
    private static UserLog userLog = null;
    private ConnectionPool cp = null;

    private UserLog() {
        try {
            cp = ConnectionPool.getInstance();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }


    public static synchronized UserLog getInstance() {
        if (userLog == null) {
            userLog = new UserLog();
        }
        return userLog;
    }


    public synchronized boolean logRecord(String userID, String ipAddress, String description) {
        Connection conn = null;
        PreparedStatement stmt = null;
        boolean status = false;

        try {
            conn = this.getJDBCConnection();
            stmt = conn.prepareStatement("insert into user_log (txnid,txndate,txntime,userid,ip_address,description) "
                    + "select (max(txnid)+1),current_date,current_time,?,?,? from user_log");

            stmt.setString(1, userID);
            stmt.setString(2, ipAddress);
            stmt.setString(3, description.trim());

            if (stmt.executeUpdate() > 0) {
                status = true;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (stmt != null) {
                    stmt.close();
                    stmt = null;
                }
            } catch (Exception ex) {
            }
            this.releaseJDBCConnection(conn);
        }

        return status;
    }

    /**
     * Get a database connection from the connection pool
     */
    private synchronized Connection getJDBCConnection() {
        Connection conn = null;
        try {
            conn = cp.getConnection();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return conn;
    }

    /**
     * Release database connection to the connection pool
     */
    private synchronized void releaseJDBCConnection(Connection conn) {
        try {
            conn.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }
}
