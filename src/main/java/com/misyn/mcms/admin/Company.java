/*
 * To change this template=""; choose Tools | Templates
 * and open the template in the editor.
 */
package com.misyn.mcms.admin;

import java.io.Serializable;
public class Company implements Serializable {

    private int n_comid = 0;
    private String v_comCode = "";
    private String v_description = "";
    private String v_add1 = "";
    private String v_add2 = "";
    private String v_telno = "";
    private String v_contperson = "";
    private String v_contemail = "";
    private String v_comstatus = "";
    private String v_inpstat = "";
    private String v_inpuser = "";
    private String d_inptime = "1900-01-01 12:00:00";
    private String v_auth1stat = "";
    private String v_auth1user = "";
    private String d_auth1time = "1900-01-01 12:00:00";
    private String v_auth2stat = "";
    private String v_auth2user = "";
    private String d_auth2time = "1900-01-01 12:00:00";

    private String errorMessage = "";

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public String getD_auth1time() {
        return d_auth1time;
    }

    public void setD_auth1time(String d_auth1time) {
        this.d_auth1time = d_auth1time;
    }

    public String getD_auth2time() {
        return d_auth2time;
    }

    public void setD_auth2time(String d_auth2time) {
        this.d_auth2time = d_auth2time;
    }

    public String getD_inptime() {
        return d_inptime;
    }

    public void setD_inptime(String d_inptime) {
        this.d_inptime = d_inptime;
    }

    public int getN_comid() {
        return n_comid;
    }

    public void setN_comid(int n_comid) {
        this.n_comid = n_comid;
    }

    public String getV_add1() {
        return v_add1;
    }

    public void setV_add1(String v_add1) {
        this.v_add1 = v_add1;
    }

    public String getV_add2() {
        return v_add2;
    }

    public void setV_add2(String v_add2) {
        this.v_add2 = v_add2;
    }

    public String getV_auth1stat() {
        return v_auth1stat;
    }

    public void setV_auth1stat(String v_auth1stat) {
        this.v_auth1stat = v_auth1stat;
    }

    public String getV_auth1user() {
        return v_auth1user;
    }

    public void setV_auth1user(String v_auth1user) {
        this.v_auth1user = v_auth1user;
    }

    public String getV_auth2stat() {
        return v_auth2stat;
    }

    public void setV_auth2stat(String v_auth2stat) {
        this.v_auth2stat = v_auth2stat;
    }

    public String getV_auth2user() {
        return v_auth2user;
    }

    public void setV_auth2user(String v_auth2user) {
        this.v_auth2user = v_auth2user;
    }

    public String getV_comCode() {
        return v_comCode;
    }

    public void setV_comCode(String v_comCode) {
        this.v_comCode = v_comCode;
    }

    public String getV_comstatus() {
        return v_comstatus;
    }

    public void setV_comstatus(String v_comstatus) {
        this.v_comstatus = v_comstatus;
    }

    public String getV_contemail() {
        return v_contemail;
    }

    public void setV_contemail(String v_contemail) {
        this.v_contemail = v_contemail;
    }

    public String getV_contperson() {
        return v_contperson;
    }

    public void setV_contperson(String v_contperson) {
        this.v_contperson = v_contperson;
    }

    public String getV_description() {
        return v_description;
    }

    public void setV_description(String v_description) {
        this.v_description = v_description;
    }

    public String getV_inpstat() {
        return v_inpstat;
    }

    public void setV_inpstat(String v_inpstat) {
        this.v_inpstat = v_inpstat;
    }

    public String getV_inpuser() {
        return v_inpuser;
    }

    public void setV_inpuser(String v_inpuser) {
        this.v_inpuser = v_inpuser;
    }

    public String getV_telno() {
        return v_telno;
    }

    public void setV_telno(String v_telno) {
        this.v_telno = v_telno;
    }
}
