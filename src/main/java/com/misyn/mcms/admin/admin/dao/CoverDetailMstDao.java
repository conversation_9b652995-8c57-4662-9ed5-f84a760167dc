package com.misyn.mcms.admin.admin.dao;

import com.misyn.mcms.admin.admin.dto.CoverDetailMstDto;
import com.misyn.mcms.claim.dao.BaseDao;

import java.sql.Connection;
import java.util.List;

public interface CoverDetailMstDao extends BaseDao<CoverDetailMstDto> {

    String SELECT_ALL_BENEFIT_COVER_DETAIL = "SELECT * FROM benefit_cover_loading_detail_mst Where `IS_UPDATED` = 'N' ";
    String SELECT_ALL_CWE_DETAIL = "SELECT * FROM cwe_detail_mst Where `IS_UPDATED` = 'N' ";
    String SELECT_ALL_CHARGES_DETAIL = "SELECT * FROM charges_discount_mst Where `IS_UPDATED` = 'N' ";
    String SEARCH_ALL_BENEFIT_COVER_DETAIL = "SELECT * FROM benefit_cover_loading_detail_mst";
    String SEARCH_ALL_CWE_DETAIL = "SELECT * FROM cwe_detail_mst";
    String SEARCH_ALL_CHARGES_DETAIL = "SELECT * FROM charges_discount_mst";
    String EMPTY_ALL_COVER_TABLE = "DELETE FROM cover_detail";
    String EMPTY_ALL_SERVICE_FACTOR_TABLE = "DELETE FROM service_factor_detail;";
    String EMPTY_ALL_BENEFIT_TABLE = "DELETE FROM benefit_detail;";
    String EMPTY_ALL_SPECIAL_PACKAGE_TABLE = "DELETE FROM special_package_detail;";
    String EMPTY_ALL_CONDITION_TABLE = "DELETE FROM condition_and_exclusion_detail;";

    String SELECT_ALL_BY_PRODUCT_ID = "SELECT * FROM cover_detail;";
    String SELECT_ALL_SRCCTC_MST = "SELECT * FROM srcc_tc_mst;";

    String INSERT_INTO_COVER_PRODUCT_WISE = "INSERT INTO cover_detail VALUES (?,?,?);";
    String UPDATE_ALL_BENEFIT_COVER_DETAIL = "Update benefit_cover_loading_detail_mst SET IS_UPDATED = 'Y' WHERE V_CODE IN ";
    String UPDATE_ALL_CWE_DETAIL = "UPDATE cwe_detail_mst SET IS_UPDATED = 'Y' WHERE V_CODE IN ";
    String UPDATE_ALL_CHARGES_DETAIL = "UPDATE  charges_discount_mst SET IS_UPDATED = 'Y' WHERE V_CODE IN ";
    String UPDATE_ALL_SRCC_TC_DETAIL = "UPDATE  srcc_tc_mst SET IS_UPDATED = 'Y' WHERE V_CODE IN ";

    String UPDATE_ALL_BENEFIT_COVER_DETAIL_STATUS= "Update benefit_cover_loading_detail_mst SET IS_UPDATED = ?";
    String UPDATE_ALL_CWE_DETAIL_STATUS = "UPDATE cwe_detail_mst SET IS_UPDATED = ?";
    String UPDATE_ALL_CHARGES_DETAIL_STATUS = "UPDATE charges_discount_mst SET IS_UPDATED = ?";
    String UPDATE_ALL_SRCC_TC_DETAIL_STATUS = "UPDATE srcc_tc_mst SET IS_UPDATED = ?";

    List<CoverDetailMstDto> searchAll(Connection connection) throws Exception;

    CoverDetailMstDto searchMaster(Connection connection, Object id) throws Exception;

    List<CoverDetailMstDto> searchAdminCoverDetail(Connection connection) throws  Exception;

    List<CoverDetailMstDto> searchAllBenefitCoverLoadingDetail(Connection connection) throws Exception;

    List<CoverDetailMstDto> searchAllCWEDetail(Connection connection) throws Exception;

    List<CoverDetailMstDto> searchAllSrccTcDetail(Connection connection) throws Exception;

    List<CoverDetailMstDto> searchAllChargesAndDiscountDetail(Connection connection) throws Exception;

    void EmptyAllCoverDetail(Connection connection) throws Exception;

    void EmptyAllBenefitDetail(Connection connection) throws Exception;

    void EmptyAllServiceFactorDetail(Connection connection) throws Exception;

    void EmptyAllSpecialPackageDetail(Connection connection) throws Exception;

    void EmptyAllConditionDetail(Connection connection) throws Exception;

    List<CoverDetailMstDto> searchAllChargesAndDiscountDetail(Connection connection,String code, String name) throws  Exception;

    List<CoverDetailMstDto> searchAllBenefitCoverLoadingDetailMater(Connection connection,String code, String name) throws Exception;

    List<CoverDetailMstDto> searchAllCWEDetailMater(Connection connection,String code, String name) throws Exception;

    void updateAllChargesAndDiscountDetailByListIn(Connection connection,List<String> codeList,String status) throws  Exception;

    void updateAllBenefitCoverLoadingDetailMaterByListIn(Connection connection,List<String> codeList,String status) throws Exception;

    void updateAllCWEDetailMaterByListIn(Connection connection,List<String> codeList,String status) throws Exception;

    void updateAllSRCCTcDetailMaterByListIn(Connection connection,List<String> codeList,String status) throws Exception;

    void updateStatusChargesAndDiscountDetail(Connection connection,String status) throws  Exception;

    void updateStatusBefitCoverLoadingDetailMater(Connection connection,String status) throws Exception;

    void updateStatusCWEDetailMater(Connection connection,String status) throws Exception;

    void updateStatusSRCCTcDetailMater(Connection connection,String status) throws Exception;
}
