package com.misyn.mcms.admin.admin.dao;

import com.misyn.mcms.admin.admin.dto.BranchDetailDto;
import com.misyn.mcms.claim.dao.BaseDao;
import com.misyn.mcms.claim.dto.DataGridDto;
import com.misyn.mcms.claim.dto.FieldParameterDto;

import java.sql.Connection;
import java.util.List;

public interface BranchMstDao extends BaseDao<BranchDetailDto> {

    String INSERT_MASTER = "INSERT INTO branch_mst VALUES (0, ?, ?, ?, ?, ?, ?);";

    String UPDATE_MASTER = "UPDATE branch_mst \n" +
            "SET `branch_name` =?,\n" +
            "`branch_city` = ?,\n" +
            "`input_user` = ?,\n" +
            "`input_datetime` = ?,\n" +
            "`record_status` = ? \n" +
            "WHERE\n" +
            "	`branch_code` = ?;";

    String SELECT_ALL_BY_RECORD_STATUS_EQUALS_ACTIVE = "SELECT * FROM branch_mst WHERE record_status = 'A' ";

    String COUNT_ALL_BY_RECORD_STATUS_EQUALS_ACTIVE = "SELECT Count(*) AS cnt FROM branch_mst WHERE record_status = 'A' ";

    String SELECT_ALL_FROM_BRANCH_CODE = "SELECT * FROM branch_mst WHERE branch_code = ?";

    String SELECT_ONE_BY_BRANCH_CODE_AND_RECORD_STATUS = "SELECT 1 FROM branch_mst WHERE branch_code= ? AND record_status = ?";

    BranchDetailDto getBranchDetailByBranchCode(Connection connection, String branchCode);

    DataGridDto getDataGridDto(Connection connection, List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField);

    boolean isAlreadyHaveRecordByBranchCodeAndRecordStatus(Connection connection, String branchCode, String recordStatus);

}
