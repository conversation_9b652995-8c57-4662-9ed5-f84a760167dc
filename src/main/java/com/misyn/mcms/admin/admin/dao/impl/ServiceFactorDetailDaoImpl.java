package com.misyn.mcms.admin.admin.dao.impl;

import com.misyn.mcms.admin.admin.dao.ServiceFactorDetailDao;
import com.misyn.mcms.admin.admin.dto.ServiceFactorDetailDto;
import com.misyn.mcms.claim.dao.AbstractBaseDao;
import com.misyn.mcms.utility.AppConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
public class ServiceFactorDetailDaoImpl extends AbstractBaseDao<ServiceFactorDetailDaoImpl> implements ServiceFactorDetailDao {

    private static final Logger LOGGER = LoggerFactory.getLogger(ServiceFactorDetailDaoImpl.class);

    @Override
    public List<ServiceFactorDetailDto> searchAll(Connection connection) throws Exception {
        return null;
    }

    @Override
    public String getMessage(Connection connection, int messageId) {
        return null;
    }

    @Override
    public ServiceFactorDetailDto insertMaster(Connection connection, ServiceFactorDetailDto serviceFactorDetailDto) throws Exception {
        try (PreparedStatement ps = connection.prepareStatement(INSERT_INTO_SERVICE_FACTOR_PRODUCT_WISE)) {
            ps.setInt(1, AppConstant.ZERO_INT);
            ps.setString(2, serviceFactorDetailDto.getCode());
            ps.setString(3, serviceFactorDetailDto.getServiceFactorName());
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
        return serviceFactorDetailDto;
    }

    @Override
    public ServiceFactorDetailDto updateMaster(Connection connection, ServiceFactorDetailDto serviceFactorDetailDto) throws Exception {
        return null;
    }

    @Override
    public ServiceFactorDetailDto insertTemporary(Connection connection, ServiceFactorDetailDto serviceFactorDetailDto) throws Exception {
        return null;
    }

    @Override
    public ServiceFactorDetailDto updateTemporary(Connection connection, ServiceFactorDetailDto serviceFactorDetailDto) throws Exception {
        return null;
    }

    @Override
    public ServiceFactorDetailDto insertHistory(Connection connection, ServiceFactorDetailDto serviceFactorDetailDto) throws Exception {
        return null;
    }

    @Override
    public boolean deleteMaster(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public boolean deleteTemporary(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public ServiceFactorDetailDto searchMaster(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public List<ServiceFactorDetailDto> searchByProductId(Connection connection) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        List<ServiceFactorDetailDto> serviceFactorDetailDtoList = new ArrayList<>();
        try {
            ps = connection.prepareStatement(SELECT_ALL_BY_PRODUCT_ID);
            rs = ps.executeQuery();
            while (rs.next()) {
                serviceFactorDetailDtoList.add(setServiceFactorDetailDto(rs));
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
        return serviceFactorDetailDtoList;
    }

    @Override
    public ServiceFactorDetailDto searchTemporary(Connection connection, Object id) throws Exception {
        return null;
    }

    private ServiceFactorDetailDto setServiceFactorDetailDto(ResultSet rs) throws SQLException {
        ServiceFactorDetailDto detailDto = new ServiceFactorDetailDto();
        detailDto.setId(rs.getInt("N_S_FACTOR_ID"));
        detailDto.setServiceFactorName(rs.getString("V_SERVICE_FACTOR_NAME"));
        detailDto.setCode(rs.getString("V_CODE"));
        return detailDto;
    }
}
