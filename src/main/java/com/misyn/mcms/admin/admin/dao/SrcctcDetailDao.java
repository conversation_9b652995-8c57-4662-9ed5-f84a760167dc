package com.misyn.mcms.admin.admin.dao;

import com.misyn.mcms.admin.admin.dto.SrcctcDetailDto;

import java.sql.Connection;
import java.util.List;

public interface SrcctcDetailDao {
    String SELECT_ALL = "SELECT * FROM srcc_tc_mst;";

    String INSERT_INTO_SPECIAL_PACKAGE_DETAIL = "INSERT INTO srcc_tc_mst VALUES (?,?,?);";

    List<SrcctcDetailDto> searchAll(Connection connection) throws Exception;
}
