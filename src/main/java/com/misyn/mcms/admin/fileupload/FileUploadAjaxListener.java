/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package com.misyn.mcms.admin.fileupload;

import org.apache.commons.fileupload2.core.ProgressListener;
public class FileUploadAjaxListener implements ProgressListener {

    private volatile long bytesRead = 0L, contentLength = 0L, item = 0L;

    public FileUploadAjaxListener() {
        super();
    }

    public void update(long aBytesRead, long aContentLength, int anItem) {
        bytesRead = aBytesRead;
        contentLength = aContentLength;
        item = anItem;
    }

    public long getBytesRead() {
        return bytesRead;
    }

    public long getContentLength() {
        return contentLength;
    }

    public long getItem() {
        return item;
    }

}
