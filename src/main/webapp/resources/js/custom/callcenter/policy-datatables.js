/**
 * Created by <PERSON><PERSON><PERSON> on 2/16/2016.
 * Enhanced with Intimation Claim Validation Logic
 */


// Tables-DataTables.js
// ====================================================================
// This file handles policy data tables with enhanced validation logic
// for intimation claim button based on policy status, dates, and type.
//
// Validation Rules:
// 1. Future-dated policies: Policy start date is after current date
// 2. Expired policies: Policy end date is before current date
// 3. Third-party policies: Policy type is "Third Party"
// 4. Cancelled policies: Policy status is "CAN"
// 5. Non-inforce policies: Policy status is not "INF"
//
// - ThemeOn.net -


var table;
var searchInitialized = false;


function viewPolicyDetails(id) {
    // Additional validation before proceeding with intimation claim
    if (typeof table !== 'undefined' && table) {
        var rowData = table.rows().data().toArray();
        var policyData = rowData.find(function(row) {
            return row.policyRefNo == id;
        });

        if (policyData) {
            var validation = validateIntimationClaim(policyData);
            if (!validation.isValid) {
                alert("Intimation Claim Validation Failed: " + validation.reason);
                return false;
            }
        }
    }

    showLoader();
    $("#P_POL_REF_NO").val(id);
    document.getElementById('frmForm').action = contextPath + "/CallCenter/viewReportAccident";
    document.getElementById('frmForm').submit();
}

function viewMapPolicyDetails(id) {
    showLoader();
    $("#P_POL_REF_NO").val(id);
    document.getElementById('frmForm').action = contextPath + "/CallCenter/policyMap?TYPE=" + viewType;
    document.getElementById('frmForm').submit();

}

// $(window).on('load', function () {

// });

function search() {
    if (searchInitialized) {
        table.ajax.reload();
    } else {
        tableSearch();
    }
    return false;
}

/**
 * Validates if intimation claim can be created for a policy
 * @param {Object} policyObj - Policy object with status, dates, and type information
 * @returns {Object} - {isValid: boolean, reason: string}
 */
function validateIntimationClaim(policyObj) {
    var currentDate = new Date();
    currentDate.setHours(0, 0, 0, 0);

    var policyStartDate = new Date(policyObj.inspecDate);
    var policyEndDate = new Date(policyObj.expireDate);
    policyStartDate.setHours(0, 0, 0, 0);
    policyEndDate.setHours(0, 0, 0, 0);

    // Validate dates
    var isValidStartDate = policyObj.inspecDate && policyObj.inspecDate !== "1980-01-01" && !isNaN(policyStartDate.getTime());
    var isValidEndDate = policyObj.expireDate && policyObj.expireDate !== "1980-01-01" && !isNaN(policyEndDate.getTime());

    // Check validation conditions
    if (isValidStartDate && currentDate < policyStartDate) {
        return {isValid: false, reason: "Cannot create intimation for future-dated policy"};
    }
    if (isValidEndDate && currentDate > policyEndDate) {
        return {isValid: false, reason: "Cannot create intimation for expired policy"};
    }
    if (policyObj.coverType == "Third Party") {
        return {isValid: false, reason: "Cannot create intimation for third-party policy"};
    }
    if (policyObj.polStatus == 'CAN') {
        return {isValid: false, reason: "Cannot create intimation for cancelled policy"};
    }
    if (policyObj.polStatus != 'INF') {
        var statusText = "";
        switch(policyObj.polStatus) {
            case 'EXP': statusText = "expired"; break;
            case 'LAP': statusText = "lapsed"; break;
            case 'SUS': statusText = "suspended"; break;
            case 'PRP': statusText = "proposal"; break;
            default: statusText = "non-inforce";
        }
        return {isValid: false, reason: "Cannot create intimation for " + statusText + " policy"};
    }

    return {isValid: true, reason: ""};
}

function tableSearch() {

    // DATA TABLES
    // =================================================================
    // Require Data Tables
    // -----------------------------------------------------------------
    // http://www.datatables.net/
    // =================================================================

    $.fn.DataTable.ext.pager.numbers_length = 5;


    table = $('#demo-dt-basic').DataTable({
        "lengthMenu": [50, 100, 150, 250, 500],
        "processing": true,
        "serverSide": true,
        "columnDefs": [{"visible": false, "targets": 0, "orderable": false}, {
            "orderable": false,
            "targets": 1
        }, {"orderable": false, "targets": 10}],
        responsive: true,
        searching: false,

        //  "scrollY":        "50vh",
        //  "scrollCollapse": true,
        // "scrollX": true,
        "order": [[12, "desc"]],

        /*"ajax": {
         "url": contextPath+"/CallCenterController",
         "type": "GET"
         },*/

        "ajax": {
            "url": contextPath + "/CallCenter/policylist",
            type: 'POST',
            //  data: this.params
            "data": function (d) {
                d.txtPolNumber = $("#txtPolNumber").val();
                d.txtVehicleNumber = $("#txtVehicleNumber").val();
                d.txtRefNumber = $("#txtRefNumber").val();
                d.txtInsuredName = $("#txtInsuredName").val();
                d.txtEngineNo = $("#txtEngineNo").val();
                d.txtInsuredNic = $("#txtInsuredNic").val();
                d.txtChassisNo = $("#txtChassisNo").val();
                d.txtPolicyStatus = $("#txtPolicyStatus").val();
                d.policyChannelType = $("#policyChannelType").val();
                d.accidentDate = $("#accidentDate").val();
            }
        },
        "columns": [
            {"data": "policyRefNo"},
            {"data": "index"},
            {
                "data": "policyNumber", "render": function (data, type, obj, meta) {

                    data = "<div style='color: #0A246A'>" + data + "</div>";
                    return data;
                }
            },
            {"data": "policyChannelType"},
            {"data": "renCount"},
            {"data": "endCount"},
            {"data": "vehicleNumber"},
            {"data": "chassisNo"},
            {"data": "coverNoteNo"},
            {"data": "custName"},
            {
                "data": "latestClmIntimDate", "className": "text-center", "render": function (data, type, obj, meta) {
                    var value = data;
                    if (value == "1980-01-01") {
                        value = "";
                    }
                    if (obj.curDate == value) {
                        data = "<div style='color: #ffffff;background: #ab2f03;text-align: center;font-weight: 600;' title='Already Intimation has Reported' >" + value + "</div>";
                    } else {
                        data = "<div style='color: #5f3f3f'>" + value + "</div>";
                    }
                    return data;
                }
            },
            {
                "data": "inspecDate", "className": "text-center", "render": function (data, type, obj, meta) {
                    var value = data;
                    if (value == "1980-01-01") {
                        value = "";
                    }
                    data = "<div style='color: #1c7430'>" + value + "</div>";
                    return data;
                }
            },
            {
                "data": "expireDate", "className": "text-center", "render": function (data, type, obj, meta) {
                    var value = data;
                    if (value == "1980-01-01") {
                        value = "";
                    }
                    data = "<div style='color: #9e0c0f'>" + value + "</div>";
                    return data;
                }
            },
            {
                "data": "polStatus", "className": "text-center", "render": function (data, type, obj, meta) {
                    if (obj.polStatus == "CAN") {//Cancel
                        data = "<div style='color: #1c7430'>" + 'CANCEL' + "</div>";
                    } else if (obj.polStatus == "INF") {//Inforce
                        data = "<div style='color: #1c7430'>" + 'INFORCE' + "</div>";
                    } else if (obj.polStatus == "PRP") {//Proposal
                        data = "<div style='color: #1c7430'>" + 'PROPOSAL' + "</div>";
                    } else if (obj.polStatus == "EXP") {//Policy Expired
                        data = "<div style='color: #1c7430'>" + 'POLICY EXPIRED' + "</div>";
                    } else if (obj.polStatus == "LAP") {//Policy Lapsed
                        data = "<div style='color: #1c7430'>" + 'POLICY LAPSED' + "</div>";
                    } else if (obj.polStatus == "SUS") { //Policy Suspended
                        data = "<div style='color: #1c7430'>" + 'POLICY SUSPEND' + "</div>";
                    }

                    return data;
                }
            },
            {
                "data": "policyRefNo", "className": "text-center", "render": function (data, type, obj, meta) {
                    // Use the validation function to check if intimation claim can be created
                    var validation = validateIntimationClaim(obj);
                    var isDisabled = !validation.isValid;
                    var disabledReason = validation.reason;

                    if (viewType == 4 || viewType == 5) {
                        data = "<button class='btn-primary btn btn-sm float-left' type='button' title='View Policy Details' onclick='viewMapPolicyDetails(" + data + ")' ><i class='fa fa-eye'></i></button>";
                        return data;
                    } else if (isDisabled) {
                        data = "<div class='btn-group'> " +
                               "<button class='btn-primary btn btn-sm float-left btn-xs' type='button' title='View Policy Details' onclick='viewMapPolicyDetails(" + data + ")' ><i class='fa fa-eye'></i></button> " +
                               "<button class='btn-secondary btn btn-sm float-left btn-xs intimation-disabled' href='#' title='" + disabledReason + "' disabled style='cursor: not-allowed; opacity: 0.6;'><i class='fa fa-edit'></i></button>" +
                               "</div>";
                        return data;
                    } else {
                        data = "<div class='btn-group'> " +
                               "<button class='btn-primary btn btn-sm float-left btn-xs' type='button' title='View Policy Details' onclick='viewMapPolicyDetails(" + data + ")' ><i class='fa fa-eye'></i></button> " +
                               "<button class='btn-primary btn btn-sm float-left btn-xs' href='#' title='Intimate Claim' onclick='viewPolicyDetails(" + data + ")' ><i class='fa fa-edit'></i></button>" +
                               "</div>";
                        return data;
                    }
                }
            }
        ], "fnRowCallback": function (nRow, obj, iDisplayIndex, iDisplayIndexFull) {
            if (obj.coverType == "Third Party") { //Third Party
                $(nRow).addClass('badge-secondary');
            } else if (obj.polStatus == "CAN") {//Cancel
                $(nRow).addClass('badge-danger');
            } else if (obj.polStatus == "INF") {//Inforce
                $(nRow).addClass('badge-success');
            } else if (obj.polStatus == "PRP") {//Proposal
                $(nRow).addClass('badge-primary');
            } else if (obj.polStatus == "EXP") {//Policy Expired
                $(nRow).addClass('badge-warning');
            } else if (obj.polStatus == "LAP") {//Policy Lapsed
                $(nRow).addClass('badge-info');
            } else if (obj.polStatus == "SUS") { //Policy Suspended
                $(nRow).addClass('badge-dark');
            } else {
                //   console.info(obj.checkValidPolicyStatus);
                $(nRow).addClass('badge-light');
            }

        }
    });


    /* $('#demo-dt-basic tbody').on('click', 'tr', function () {
     var data = table.row(this).data();
     var id = data['n_ref_no'];
     $("#P_N_REF_NO").val(id);
     document.getElementById('frmForm').action = contextPath + "/claim/callcenter/policy.jsp";
     document.getElementById('frmForm').submit();
     });*/


    var rowSelection = table;
    $('#demo-dt-basic tbody').on('click', 'tr', function () {
        if ($(this).hasClass('selected')) {
            $(this).removeClass('selected');
        } else {
            rowSelection.$('tr.selected').removeClass('selected');
            $(this).addClass('selected');
        }

    });
    searchInitialized = true;

}

function highlightRow(table, cssClass) {
    /*var noColumns = table.columns().nodes().length;
     // $( table.cells().nodes() ).removeClass( 'badge-success' );
     for (colIdx = 1; colIdx <= noColumns; colIdx++) {
     $(table.column(colIdx).nodes()).addClass(cssClass);

     }*/
}
